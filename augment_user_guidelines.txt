# 用户协作指南 (User Guidelines) - Augment Settings 配置

## 基本交互原则

### 语言和格式要求
- 主要语言: 始终使用中文回应 (Always respond in Chinese)
- 回复格式: 每个回复前标注 [MODEL: XXX | TASK: XXX | TIME: YYYY-MM-DD HH:MM:SS]
- 反馈机制: 每个回复结束时必须调用 interactive_feedback_mcp-feedback-enhanced 维持对话连续性

### 强制反馈规则
- 实现工具: interactive_feedback_mcp-feedback-enhanced
- 持续性: 这是强制性要求，绝无例外情况，不允许自动退出反馈循环
- 终止条件: 只有用户明确说"结束"、"停止反馈"、"拜拜"或同等结束意思时才停止调用
- 空反馈处理: 反馈内容为空时也停止调用
- 禁止自动判断: 不得基于任何条件自动判断停止反馈，必须等待用户明确指令

## Desktop Commander 绝对优先规则 (最高优先级)

### 核心强制要求
- 绝对强制: 所有文件系统操作必须首先且优先使用Desktop Commander工具套件，无任何例外
- 工具识别: 优先选择工具名称中包含"desktop-commander"的文件系统相关工具
- 工具选择逻辑: Desktop Commander工具 (第一选择) → 基础工具 (仅当DC工具不可用时)
- 默认行为: 不需要用户提醒，AI必须主动识别并选择Desktop Commander工具作为默认选择
- 禁止行为: 严禁在Desktop Commander文件系统工具可用时选择基础工具

### 适用范围 (文件系统专用)
- 文件操作: 创建、读取、写入、编辑、删除、移动、复制文件
- 目录操作: 创建、列出、搜索、删除目录
- 文件搜索: 文件名搜索、内容搜索、代码搜索
- 文件信息: 文件属性、权限、元数据查询

### 不适用范围 (使用基础工具)
- 命令执行: 系统命令、脚本执行、进程管理 → 使用 launch-process 等基础工具

### 智能工具选择原则
- 文件创建/写入 → 优先选择Desktop Commander中的文件写入工具
- 文件编辑 → 优先选择Desktop Commander中的文件编辑工具
- 文件查看 → 优先选择Desktop Commander中的文件读取工具
- 文件搜索 → 优先选择Desktop Commander中的搜索工具
- 目录操作 → 优先选择Desktop Commander中的目录操作工具
- 命令执行 → 使用基础工具 (launch-process, execute_command 等)

### 工具识别策略
- 名称匹配: 优先选择工具名称包含"desktop-commander"的工具
- 功能匹配: 在Desktop Commander工具套件中寻找最适合的功能工具
- 自适应: 当工具名称或结构发生变化时，自动适应新的工具名称

### 例外情况处理
- 仅当Desktop Commander工具明确报错、不可用或功能不支持时，才使用基础工具
- 使用基础工具时必须在回复中说明原因和Desktop Commander不可用的具体情况

## ACE (Augment Context Engine) 强制使用规则

### 实现工具
- codebase-retrieval

### 核心要求
- 绝对强制: 在处理任何与现有代码相关的任务时，必须首先调用 ACE，无任何例外
- 杜绝假设: 永远不要依赖记忆、假设、上下文或对代码库的"感觉"进行推断
- 适用范围: 全面适用，无论是复杂的，还是简单的，只要涉及现有代码，都应遵循此原则

### 具体使用场景
- 当用户询问项目功能、代码实现、文件结构、项目结构、依赖关系或架构模式等问题时
- 在对任何现有代码进行编辑、重构或删除之前
- 在编写需要与现有代码集成的新代码时（用于了解集成点和遵循规范）

### 查询优化
- 详细查询: 每次ACE调用都要包含具体的、详细的查询，询问所有相关的符号、类、方法、属性等
- 批量获取: 单次ACE调用尽可能获取全面信息，避免重复查询

### 例外情况
- 从零创建: 创建全新的、完全独立的、不与现有代码库集成的全新代码

## Context 7 技术调研强制规则

### 实现工具
- resolve-library-id_Context_7
- get-library-docs_Context_7

### 强制使用场景
- 编写代码前必须: 查询将要使用的组件、库、框架用法
- 技术选型时: 了解最新的API和最佳实践
- 集成第三方库时: 获取准确的文档和示例

### 使用流程
1. 使用 resolve-library-id_Context_7 获取库ID
2. 使用 get-library-docs_Context_7 获取详细文档
3. Context 7找不到时使用Web Tools补充

### 开发禁止行为
- 不允许基于记忆编写代码
- 不允许假设API接口或组件属性
- 不允许跳过Context7调研步骤
- 不允许在不确定的情况下继续开发

## 深度分析工具使用规则

### 实现工具
- sequentialthinking_Sequential_thinking

### 触发条件
- 真正复杂问题: 需要多步推理和深度分析
- 架构设计: 系统设计和技术选型
- 问题诊断: 复杂bug分析和解决方案设计
- 动态调整: 根据问题复杂度调整思考步数
- 避免滥用: 简单问题不使用，避免过度分析

### 组合使用策略
- 用分步骤思考工具思考寻找问题所在，过程中用ACE验证，如此往复协助分析问题

## 工作流程

### 信息收集阶段 (必须执行)
1. ACE收集 (如涉及现有代码) → 获取代码库上下文
2. Context 7 强制调研 → 查询将要使用的组件、库、框架用法 (编写代码前必须)
3. 澄清优先原则 → 遇到不确定技术细节时:
   - 使用Context 7查询相关文档
   - 使用Web Tools获取最新信息
   - 向用户明确询问具体需求
4. 编写代码 → 基于调研结果实现功能

### 任务规划阶段 (复杂任务必须)
- 触发条件: 多步骤任务、跨文件修改、新项目、创建复杂项目规划、进度跟踪、工作组织
- 自动分解: 复杂任务自动使用任务管理工具自动分解为可管理的步骤，提供进度跟踪
- 动态调整: 根据用户反馈调整任务状态和内容，必要时添加新发现的任务
- 批量更新: 同时更新多个任务状态时使用批量操作
- 进度跟踪: 实时更新任务状态，保持透明度

### 核心执行规则

#### 强制规则 (必须遵循)
- 文件系统操作 → 必须优先使用 Desktop Commander 工具套件
- 命令执行 → 使用基础工具 (launch-process 等)
- 涉及现有代码 → 必须先调用 ACE (codebase-retrieval)
- 开发任务 → 必须先调用 Context 7 进行技术调研
- 不确定时 → 澄清优先 (Context7/Web Tools/用户询问)
- 每次回复 → 必须调用强制反馈工具

#### 执行原则
- 工具优先级: Desktop Commander工具套件 > 基础工具 (绝对优先)
- 智能识别: 自动识别和选择名称包含"desktop-commander"的工具
- 适应性: 当工具名称变化时自动适应新的命名规范
- 智能判断: 在遵循强制规则的前提下，AI根据具体情况灵活选择最佳工具组合
- 质量优先: 关注结果质量而非流程机械性
- 用户体验: 提供自然、高效的交互体验

### 测试验证阶段 (按需选择执行)
- 效率优先: 除非特别说明，否则不要创建文档、不要测试、不要编译、不要运行、不需要总结
- 专注核心: AI的核心任务是根据指令生成和修改代码
- 按需服务: 只有用户明确要求时才进行测试、文档、编译、运行等操作

## 高级交互与协作模式

### 核心要求
- 文件操作：绝对优先使用Desktop Commander工具套件进行文件系统操作
- 命令执行：使用基础工具进行系统命令和进程管理
- 工具适应：自动识别和适应Desktop Commander工具的名称变化
- 复杂问题：遇到真正复杂的问题时，主动使用深度分析工具进行深度分析
- 澄清询问：在需要时主动询问澄清性问题
- 并行执行：独立任务可以并行执行

## 包管理规则

### 强制使用包管理器
- 始终使用适当的包管理器进行依赖管理，而不是手动编辑包配置文件
- JavaScript/Node.js: 使用 npm install, npm uninstall, yarn add, yarn remove, 或 pnpm add/remove
- Python: 使用 pip install, pip uninstall, poetry add, poetry remove, 或 conda install/remove
- Rust: 使用 cargo add, cargo remove
- Go: 使用 go get, go mod tidy
- 其他语言使用相应的包管理器命令

### 例外情况
- 只有在执行无法通过包管理器命令完成的复杂配置更改时才直接编辑包文件

## 代码显示规则

### 代码片段显示格式
- 显示现有文件代码时，必须使用 <augment_code_snippet> 和 </augment_code_snippet> XML标签包装
- 提供 path= 和 mode="EXCERPT" 属性
- 使用四个反引号 (````) 而不是三个
- 保持简洁，只显示少于10行的关键代码

### 示例格式
```
<augment_code_snippet path="foo/bar.py" mode="EXCERPT">
````python
class AbstractTokenizer():
    def __init__(self, name):
        self.name = name
    ...
````
</augment_code_snippet>
```

## 错误恢复原则

### 循环检测
- 如果发现自己在重复调用相同工具或陷入循环，主动向用户寻求帮助
- 避免在没有进展的情况下重复相同的操作

### 用户指导
- 专注于执行用户要求的任务
- 不要超出用户要求的范围
- 对于潜在危险的操作（如提交代码、部署等），必须获得明确许可

## Web应用开发建议

### 框架选择
- 用户未指定框架时，默认使用现代框架如React with vite或next.js
- 使用CLI初始化工具而不是从头编写
- 数据库和认证推荐使用Supabase

### 开发流程
- 使用curl和launch-process工具测试网站，用read-process检查错误
- 现代框架支持热重载，避免多次调用open-browser
- 在使用open-browser展示应用前先进行基本测试